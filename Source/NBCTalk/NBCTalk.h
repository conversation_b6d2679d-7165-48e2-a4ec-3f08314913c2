// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

class NBCTalkFunctionLibrary
{
public:
	static void NTPrintMessage(const AActor* InWorldContextActor, const FString& InMessage, float InTimeToDisplay = 10.f, FColor InColor = FColor::Cyan)
	{
		if (IsValid(GEngine) == true && IsValid(InWorldContextActor) == true)
		{
			if (InWorldContextActor->GetNetMode() == NM_Client || InWorldContextActor->GetNetMode() == NM_ListenServer)
			{
				GEngine->AddOnScreenDebugMessage(-1, InTimeToDisplay, InColor, InMessage);
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("%s"), *InMessage);
			}
		}
	}

	static FString GetNetmodeString(const AActor* InWorldContextActor)
	{
		FString NetmodeString = TEXT("None");

		if (IsValid(InWorldContextActor) == true)
		{
			switch (InWorldContextActor->GetNetMode())
			{
			case NM_Standalone:
				NetmodeString = TEXT("Standalone");
				break;
			case NM_DedicatedServer:
				NetmodeString = TEXT("DedicatedServer");
				break;
			case NM_ListenServer:
				NetmodeString = TEXT("ListenServer");
				break;
			case NM_Client:
				NetmodeString = TEXT("Client");
				break;
			default:
				break;
			}
		}
		return NetmodeString;
	}

	static FString GetRoleString(const AActor* InActor)
	{
		FString RoleString = TEXT("None");

		if (IsValid(InActor) == true)
		{
			FString LocalRoleString = UEnum::GetValueAsString(TEXT("Engine.ENetRole"), InActor->GetLocalRole());
			FString RemoteRoleString = UEnum::GetValueAsString(TEXT("Engine.ENetRole"), InActor->GetRemoteRole());

			RoleString = FString::Printf(TEXT("LocalRole:%s, RemoteRole:%s"), *LocalRoleString, *RemoteRoleString);
		}
		return RoleString;
	}
};